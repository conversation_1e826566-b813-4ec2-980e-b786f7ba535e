<div class="min-h-screen bg-white text-gray-800 flex flex-col">
  <!-- Header -->
  <header class="bg-gray-900 shadow-md border-b-8 border-orange-500 border-t-8 border-t-orange-500">
    <div class="container mx-auto px-6 py-6 flex justify-between items-center">
      <div class="text-3xl font-bold text-white">
        <a routerLink="/" class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
          </div>
          <span>Library Management</span>
        </a>
      </div>
      <nav class="hidden md:flex space-x-6">
        <a routerLink="/login" class="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-lg transition duration-300 shadow-lg">Sign In</a>
        <a href="#features" class="py-2 px-4 hover:bg-gray-800 rounded-lg text-white transition duration-300">Features</a>
        <a href="#about" class="py-2 px-4 hover:bg-gray-800 rounded-lg text-white transition duration-300">About</a>
      </nav>
      <div class="md:hidden">
        <button id="mobile-menu-button" class="text-white focus:outline-none">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
          </svg>
        </button>
      </div>
    </div>
    <div id="mobile-menu" class="hidden md:hidden border-t border-gray-700 mt-4 pt-4">
      <a routerLink="/login" class="block py-3 px-4 text-sm bg-orange-500 hover:bg-orange-600 text-white rounded-lg mb-2 text-center font-semibold">Sign In</a>
      <a href="#features" class="block py-2 px-4 text-sm hover:bg-gray-800 text-white rounded">Features</a>
      <a href="#about" class="block py-2 px-4 text-sm hover:bg-gray-800 text-white rounded">About</a>
    </div>
  </header>

  <!-- Hero Section -->
  <main class="flex-grow relative overflow-hidden">
    <!-- Background Image Container for Hero Section -->
    <div class="absolute inset-0 z-0">
      <div class="background-slideshow w-full h-full">
        <div class="slide active" style="background-image: url('https://i.imgur.com/a7dTugL.jpeg');"></div>
        <div class="slide" style="background-image: url('https://i.imgur.com/O2oKst2.png');"></div>
        <div class="slide" style="background-image: url('https://i.imgur.com/YPE0QjQ.jpeg');"></div>
      </div>
    </div>

    <!-- Overlay for better text readability -->
    <div class="absolute inset-0 bg-black bg-opacity-30 z-10"></div>

    <!-- Hero Content -->
    <div class="container mx-auto px-6 py-16 text-center flex flex-col justify-center items-center h-[calc(100vh-128px)] relative z-20">
      <h1 class="text-5xl font-extrabold mb-4 text-white drop-shadow-lg">LIBRARY MANAGEMENT <br>SYSTEM</h1>
      <p class="text-lg text-gray-200 mb-8 drop-shadow-md">Your gateway to a world of knowledge.</p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <a routerLink="/login" class="bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-6 rounded-full transition duration-300 login-tether border-2 border-white shadow-lg">LOGIN</a>
        <a href="#features" class="border-2 border-white text-white hover:bg-white hover:text-gray-900 font-semibold py-3 px-6 rounded-full transition duration-300">
          Learn More
        </a>
      </div>
      <div class="mt-8">
        <a href="#features" class="text-gray-300 hover:text-white">
          <svg class="w-8 h-8 animate-bounce drop-shadow-md" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
        </a>
      </div>
    </div>
  </main>

  <!-- Mission Section -->
  <section id="features" class="bg-gradient-to-br from-orange-100 via-orange-50 to-gray-100 py-20 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-15">
      <div class="absolute top-10 left-10 w-32 h-32 bg-orange-400 rounded-full"></div>
      <div class="absolute top-40 right-20 w-24 h-24 bg-orange-500 rounded-full"></div>
      <div class="absolute bottom-20 left-1/4 w-40 h-40 bg-orange-300 rounded-full"></div>
      <div class="absolute bottom-40 right-1/3 w-28 h-28 bg-orange-600 rounded-full"></div>
    </div>
    <div class="container mx-auto px-6 relative z-10">
      <!-- Mission Statement -->
      <div class="w-full mb-16">
        <div class="bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100 py-20 relative overflow-hidden">
          <!-- Elegant Background Pattern -->
          <div class="absolute inset-0 opacity-30">
            <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
              <defs>
                <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                  <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#3b82f6" stroke-width="0.5" opacity="0.3"/>
                </pattern>
              </defs>
              <rect width="100" height="100" fill="url(#grid)" />
            </svg>
          </div>

          <!-- Floating Elements -->
          <div class="absolute top-10 right-10 w-20 h-20 bg-gradient-to-br from-orange-400 to-orange-500 rounded-full opacity-20 animate-pulse"></div>
          <div class="absolute bottom-10 left-10 w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full opacity-20 animate-pulse" style="animation-delay: 1s;"></div>
          <div class="absolute top-1/2 right-1/4 w-12 h-12 bg-gradient-to-br from-indigo-400 to-indigo-500 rounded-full opacity-15 animate-pulse" style="animation-delay: 2s;"></div>

          <div class="container mx-auto px-6 relative z-10">
            <!-- Professional Header Section -->
            <div class="text-center mb-16">
              <!-- Excellence Badge -->
              <div class="inline-block mb-8">
                <div class="bg-white rounded-full shadow-lg border border-gray-200 px-8 py-4">
                  <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 bg-gradient-to-r from-orange-500 to-blue-500 rounded-full"></div>
                    <span class="text-sm font-semibold text-gray-700 uppercase tracking-wider">Excellence in Education</span>
                    <div class="w-3 h-3 bg-gradient-to-r from-blue-500 to-orange-500 rounded-full"></div>
                  </div>
                </div>
              </div>

              <!-- Main Title -->
              <h2 class="text-5xl md:text-7xl font-bold bg-gradient-to-r from-slate-800 via-blue-700 to-indigo-800 bg-clip-text text-transparent mb-8 leading-tight">
                BENEDICTO COLLEGE
              </h2>

              <!-- Subtitle with Professional Styling -->
              <div class="max-w-2xl mx-auto">
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200 p-8">
                  <h3 class="text-3xl font-bold text-blue-600 mb-4">
                    Your Education… Our Mission
                  </h3>
                  <div class="flex items-center justify-center mb-6">
                    <div class="w-20 h-1 bg-gradient-to-r from-orange-500 to-blue-500 rounded-full"></div>
                  </div>
                  <p class="text-lg text-gray-700 leading-relaxed">
                    As the preferred higher educational institution in the Asia-Pacific, Benedicto College will be a globally competitive institution and a catalyst in nation-building, creating a better quality of life and developing productive members of the society.
                  </p>
                </div>
              </div>
            </div>

            <!-- Professional Feature Cards -->
            <div class="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              <!-- Excellence Card -->
              <div class="group bg-white rounded-2xl shadow-xl border border-gray-200 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-500">
                <div class="text-center">
                  <div class="w-16 h-16 bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <h4 class="text-2xl font-bold text-gray-900 mb-3">Excellence</h4>
                  <p class="text-gray-600 leading-relaxed">Maintaining the highest academic standards and fostering a culture of continuous improvement in all educational endeavors.</p>
                </div>
              </div>

              <!-- Innovation Card -->
              <div class="group bg-white rounded-2xl shadow-xl border border-gray-200 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-500">
                <div class="text-center">
                  <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7"></path>
                    </svg>
                  </div>
                  <h4 class="text-2xl font-bold text-gray-900 mb-3">Innovation</h4>
                  <p class="text-gray-600 leading-relaxed">Embracing cutting-edge technology and modern teaching methodologies to prepare students for tomorrow's challenges.</p>
                </div>
              </div>

              <!-- Community Card -->
              <div class="group bg-white rounded-2xl shadow-xl border border-gray-200 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-500">
                <div class="text-center">
                  <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                  </div>
                  <h4 class="text-2xl font-bold text-gray-900 mb-3">Community</h4>
                  <p class="text-gray-600 leading-relaxed">Building strong connections and partnerships that create lasting positive impact in our local and global communities.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Features Section -->
      <div class="bg-gradient-to-br from-orange-50 via-white to-blue-50 py-20 relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-20">
          <svg class="w-full h-full" viewBox="0 0 60 60" preserveAspectRatio="none">
            <defs>
              <pattern id="hexagon" width="60" height="60" patternUnits="userSpaceOnUse">
                <polygon points="30,5 50,20 50,40 30,55 10,40 10,20" fill="none" stroke="#f97316" stroke-width="1" opacity="0.3"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#hexagon)" />
          </svg>
        </div>

        <div class="container mx-auto px-6 relative z-10">
          <!-- Section Header -->
          <div class="text-center mb-16">
            <div class="inline-block mb-6">
              <div class="bg-gradient-to-r from-orange-500 to-blue-500 rounded-full p-1 shadow-lg">
                <div class="bg-white rounded-full px-8 py-3">
                  <span class="text-sm font-bold text-black uppercase tracking-wider">Our Excellence</span>
                </div>
              </div>
            </div>
            <h2 class="text-4xl md:text-5xl font-bold text-black mb-6">
              Library Management Excellence
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Supporting our educational mission with cutting-edge library technology and innovative learning solutions
            </p>
          </div>

          <!-- Feature Cards -->
          <div class="grid md:grid-cols-3 gap-10">
            <!-- Academic Excellence Card -->
            <div class="group relative">
              <div class="absolute inset-0 bg-gradient-to-r from-gray-400 to-gray-500 rounded-3xl blur-lg opacity-25 group-hover:opacity-40 transition-opacity duration-500"></div>
              <div class="relative bg-white rounded-3xl shadow-xl border border-gray-100 p-8 hover:shadow-2xl hover:-translate-y-3 transition-all duration-500">
                <div class="text-center">
                  <!-- Floating Icon -->
                  <div class="relative mb-8">
                    <div class="w-20 h-20 bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl flex items-center justify-center mx-auto shadow-xl group-hover:scale-110 group-hover:rotate-3 transition-all duration-500">
                      <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                      </svg>
                    </div>
                    <div class="absolute -top-2 -right-2 w-6 h-6 bg-gray-600 rounded-full animate-ping"></div>
                  </div>
                  <h3 class="text-2xl font-bold text-gray-900 mb-4">Academic Excellence</h3>
                  <p class="text-gray-600 leading-relaxed">Comprehensive programs designed to develop globally competitive graduates with strong academic foundation and practical skills for the modern world.</p>
                </div>
              </div>
            </div>

            <!-- Global Perspective Card -->
            <div class="group relative">
              <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-500 rounded-3xl blur-lg opacity-25 group-hover:opacity-40 transition-opacity duration-500"></div>
              <div class="relative bg-white rounded-3xl shadow-xl border border-gray-100 p-8 hover:shadow-2xl hover:-translate-y-3 transition-all duration-500">
                <div class="text-center">
                  <!-- Floating Icon -->
                  <div class="relative mb-8">
                    <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto shadow-xl group-hover:scale-110 group-hover:rotate-3 transition-all duration-500">
                      <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                      </svg>
                    </div>
                    <div class="absolute -top-2 -right-2 w-6 h-6 bg-blue-400 rounded-full animate-ping"></div>
                  </div>
                  <h3 class="text-2xl font-bold text-gray-900 mb-4">Global Perspective</h3>
                  <p class="text-gray-600 leading-relaxed">International partnerships and exchange programs preparing students for success in the Asia-Pacific region and beyond the global marketplace.</p>
                </div>
              </div>
            </div>

            <!-- Community Impact Card -->
            <div class="group relative">
              <div class="absolute inset-0 bg-gradient-to-r from-indigo-400 to-indigo-500 rounded-3xl blur-lg opacity-25 group-hover:opacity-40 transition-opacity duration-500"></div>
              <div class="relative bg-white rounded-3xl shadow-xl border border-gray-100 p-8 hover:shadow-2xl hover:-translate-y-3 transition-all duration-500">
                <div class="text-center">
                  <!-- Floating Icon -->
                  <div class="relative mb-8">
                    <div class="w-20 h-20 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto shadow-xl group-hover:scale-110 group-hover:rotate-3 transition-all duration-500">
                      <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                      </svg>
                    </div>
                    <div class="absolute -top-2 -right-2 w-6 h-6 bg-indigo-400 rounded-full animate-ping"></div>
                  </div>
                  <h3 class="text-2xl font-bold text-gray-900 mb-4">Community Impact</h3>
                  <p class="text-gray-600 leading-relaxed">Developing productive members of society through community engagement, leadership development, and social responsibility initiatives.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="py-24 bg-gradient-to-br from-indigo-50 via-white to-orange-50 relative overflow-hidden">
    <!-- Animated Background Elements -->
    <div class="absolute inset-0 opacity-30">
      <div class="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-orange-300 to-orange-400 rounded-full animate-pulse opacity-40"></div>
      <div class="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-br from-blue-300 to-blue-400 rounded-full animate-pulse opacity-40" style="animation-delay: 1s;"></div>
      <div class="absolute top-1/2 left-1/4 w-16 h-16 bg-gradient-to-br from-indigo-300 to-indigo-400 rounded-full animate-pulse opacity-30" style="animation-delay: 2s;"></div>
    </div>

    <div class="container mx-auto px-6 relative z-10">
      <!-- Section Header -->
      <div class="text-center mb-20">
        <div class="inline-block mb-8">
          <div class="bg-gradient-to-r from-indigo-500 to-orange-500 rounded-full p-1 shadow-xl">
            <div class="bg-white rounded-full px-10 py-4">
              <span class="text-sm font-bold text-black uppercase tracking-wider">About Our Institution</span>
            </div>
          </div>
        </div>
        <h2 class="text-5xl md:text-6xl font-bold text-black mb-8 leading-tight">
          About Benedicto College
        </h2>
        <div class="max-w-5xl mx-auto">
          <p class="text-xl text-gray-700 leading-relaxed mb-8">
            Benedicto College is a premier educational institution committed to academic excellence and holistic development.
            Our Library Management System represents our dedication to providing cutting-edge technology and resources to support
            our students, faculty, and staff in their pursuit of knowledge and academic success.
          </p>
          <div class="flex items-center justify-center">
            <div class="w-24 h-1 bg-gradient-to-r from-indigo-500 to-orange-500 rounded-full"></div>
          </div>
        </div>
      </div>

      <!-- Enhanced Contact & Social Media Grid -->
      <div class="grid lg:grid-cols-2 gap-16 max-w-7xl mx-auto">
        <!-- Contact Information Card -->
        <div class="group relative">
          <div class="absolute inset-0 bg-gradient-to-r from-indigo-400 to-indigo-500 rounded-3xl blur-lg opacity-25 group-hover:opacity-40 transition-opacity duration-500"></div>
          <div class="relative bg-white rounded-3xl shadow-2xl border border-gray-100 p-10 hover:shadow-3xl hover:-translate-y-2 transition-all duration-500">
            <div class="text-center mb-8">
              <!-- Phone Design -->
              <div class="relative w-16 h-20 mx-auto mb-4">
                <div class="w-16 h-20 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl shadow-xl transform hover:scale-105 transition-transform duration-300">
                  <!-- Phone Screen -->
                  <div class="absolute top-3 left-2 right-2 bottom-6 bg-indigo-100 rounded-lg"></div>
                  <!-- Phone Speaker -->
                  <div class="absolute top-1 left-1/2 transform -translate-x-1/2 w-6 h-1 bg-indigo-300 rounded-full"></div>
                  <!-- Phone Home Button -->
                  <div class="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-indigo-300 rounded-full"></div>
                  <!-- Phone Icon -->
                  <div class="absolute inset-0 flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                  </div>
                </div>
              </div>
              <h3 class="text-3xl font-bold text-gray-900 mb-2">Get in Touch</h3>
              <p class="text-gray-600">Connect with us through multiple channels</p>
            </div>

            <div class="space-y-6">
              <div class="flex items-center p-4 bg-gradient-to-r from-indigo-50 to-blue-50 rounded-2xl hover:from-indigo-100 hover:to-blue-100 transition-all duration-300">
                <div class="w-14 h-14 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center mr-6 shadow-lg">
                  <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                </div>
                <div>
                  <p class="font-bold text-gray-900 text-lg">Campus Address</p>
                  <p class="text-gray-600">Benedicto College Campus, Philippines</p>
                </div>
              </div>

              <div class="flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl hover:from-blue-100 hover:to-indigo-100 transition-all duration-300">
                <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-6 shadow-lg">
                  <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <div>
                  <p class="font-bold text-gray-900 text-lg">Email Address</p>
                  <p class="text-gray-600">info&#64;benedictocollege.edu.ph</p>
                </div>
              </div>

              <div class="flex items-center p-4 bg-gradient-to-r from-orange-50 to-yellow-50 rounded-2xl hover:from-orange-100 hover:to-yellow-100 transition-all duration-300">
                <div class="w-14 h-14 bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl flex items-center justify-center mr-6 shadow-lg">
                  <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                  </svg>
                </div>
                <div>
                  <p class="font-bold text-gray-900 text-lg">Phone Number</p>
                  <p class="text-gray-600">+63 (XXX) XXX-XXXX</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Social Media Links Card -->
        <div class="group relative">
          <div class="absolute inset-0 bg-gradient-to-r from-orange-400 to-orange-500 rounded-3xl blur-lg opacity-25 group-hover:opacity-40 transition-opacity duration-500"></div>
          <div class="relative bg-white rounded-3xl shadow-2xl border border-gray-100 p-10 hover:shadow-3xl hover:-translate-y-2 transition-all duration-500">
            <div class="text-center mb-8">
              <!-- Envelope Design -->
              <div class="relative w-20 h-16 mx-auto mb-4">
                <div class="w-20 h-16 bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg shadow-xl transform rotate-3 hover:rotate-0 transition-transform duration-300">
                  <!-- Envelope Body -->
                  <div class="absolute inset-0 bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg"></div>
                  <!-- Envelope Flap -->
                  <div class="absolute top-0 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-8 border-r-8 border-b-6 border-l-transparent border-r-transparent border-b-gray-600"></div>
                  <!-- Envelope Icon -->
                  <div class="absolute inset-0 flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                </div>
              </div>
              <h3 class="text-3xl font-bold text-gray-900 mb-2">Connect With Us</h3>
              <p class="text-gray-600">Stay updated with our latest news and announcements</p>
            </div>

            <!-- Enhanced Social Media Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <!-- Facebook -->
              <a href="https://facebook.com/benedictocollege" target="_blank" class="group relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 hover:from-blue-100 hover:to-blue-200 transition-all duration-500 hover:shadow-xl hover:-translate-y-1">
                <div class="flex items-center">
                  <div class="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mr-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                  </div>
                  <div>
                    <p class="font-bold text-gray-900 text-lg">Facebook</p>
                    <p class="text-gray-600">Follow our updates</p>
                  </div>
                </div>
                <div class="absolute top-2 right-2 w-3 h-3 bg-blue-400 rounded-full animate-ping"></div>
              </a>

              <!-- Instagram -->
              <a href="https://instagram.com/benedictocollege" target="_blank" class="group relative overflow-hidden bg-gradient-to-br from-pink-50 to-purple-100 rounded-2xl p-6 hover:from-pink-100 hover:to-purple-200 transition-all duration-500 hover:shadow-xl hover:-translate-y-1">
                <div class="flex items-center">
                  <div class="w-16 h-16 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center mr-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                    </svg>
                  </div>
                  <div>
                    <p class="font-bold text-gray-900 text-lg">Instagram</p>
                    <p class="text-gray-600">&#64;benedictocollege</p>
                  </div>
                </div>
                <div class="absolute top-2 right-2 w-3 h-3 bg-pink-400 rounded-full animate-ping"></div>
              </a>

              <!-- YouTube -->
              <a href="https://youtube.com/benedictocollege" target="_blank" class="group relative overflow-hidden bg-gradient-to-br from-red-50 to-red-100 rounded-2xl p-6 hover:from-red-100 hover:to-red-200 transition-all duration-500 hover:shadow-xl hover:-translate-y-1">
                <div class="flex items-center">
                  <div class="w-16 h-16 bg-gradient-to-br from-red-600 to-red-700 rounded-2xl flex items-center justify-center mr-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                    </svg>
                  </div>
                  <div>
                    <p class="font-bold text-gray-900 text-lg">YouTube</p>
                    <p class="text-gray-600">Subscribe to us</p>
                  </div>
                </div>
                <div class="absolute top-2 right-2 w-3 h-3 bg-red-400 rounded-full animate-ping"></div>
              </a>

              <!-- Official Website -->
              <a href="https://benedictocollege.edu.ph" target="_blank" class="group relative overflow-hidden bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-6 hover:from-orange-100 hover:to-orange-200 transition-all duration-500 hover:shadow-xl hover:-translate-y-1">
                <div class="flex items-center">
                  <div class="w-16 h-16 bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl flex items-center justify-center mr-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                    </svg>
                  </div>
                  <div>
                    <p class="font-bold text-gray-900 text-lg">Official Website</p>
                    <p class="text-gray-600">Visit benedictocollege.edu.ph</p>
                  </div>
                </div>
                <div class="absolute top-2 right-2 w-3 h-3 bg-gray-600 rounded-full animate-ping"></div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gray-900 py-8 border-t-8 border-orange-500 border-b-8 border-b-orange-500">
    <div class="container mx-auto px-6">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <div class="text-gray-200 mb-4 md:mb-0">
          &copy; 2025 Library Management System. All Rights Reserved.
        </div>
        <div class="flex space-x-6">
          <a href="#" class="text-gray-400 hover:text-orange-400 transition duration-300">Privacy Policy</a>
          <a href="#" class="text-gray-400 hover:text-orange-400 transition duration-300">Terms of Service</a>
          <a href="#" class="text-gray-400 hover:text-orange-400 transition duration-300">Support</a>
        </div>
      </div>
    </div>
  </footer>
</div>
