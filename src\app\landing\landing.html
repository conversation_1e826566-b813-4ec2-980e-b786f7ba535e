<div class="min-h-screen bg-white text-gray-800 flex flex-col">
  <!-- Header -->
  <header class="bg-gray-900 shadow-md border-b-8 border-orange-500 border-t-8 border-t-orange-500">
    <div class="container mx-auto px-6 py-6 flex justify-between items-center">
      <div class="text-3xl font-bold text-white">
        <a routerLink="/" class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
          </div>
          <span>Library Management</span>
        </a>
      </div>
      <nav class="hidden md:flex space-x-6">
        <a routerLink="/login" class="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-lg transition duration-300 shadow-lg">Sign In</a>
        <a href="#features" class="py-2 px-4 hover:bg-gray-800 rounded-lg text-white transition duration-300">Features</a>
        <a href="#about" class="py-2 px-4 hover:bg-gray-800 rounded-lg text-white transition duration-300">About</a>
      </nav>
      <div class="md:hidden">
        <button id="mobile-menu-button" class="text-white focus:outline-none">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
          </svg>
        </button>
      </div>
    </div>
    <div id="mobile-menu" class="hidden md:hidden border-t border-gray-700 mt-4 pt-4">
      <a routerLink="/login" class="block py-3 px-4 text-sm bg-orange-500 hover:bg-orange-600 text-white rounded-lg mb-2 text-center font-semibold">Sign In</a>
      <a href="#features" class="block py-2 px-4 text-sm hover:bg-gray-800 text-white rounded">Features</a>
      <a href="#about" class="block py-2 px-4 text-sm hover:bg-gray-800 text-white rounded">About</a>
    </div>
  </header>

  <!-- Hero Section -->
  <main class="flex-grow relative overflow-hidden">
    <!-- Background Image Container for Hero Section -->
    <div class="absolute inset-0 z-0">
      <div class="background-slideshow w-full h-full">
        <div class="slide active" style="background-image: url('https://i.imgur.com/a7dTugL.jpeg');"></div>
        <div class="slide" style="background-image: url('https://i.imgur.com/O2oKst2.png');"></div>
        <div class="slide" style="background-image: url('https://i.imgur.com/YPE0QjQ.jpeg');"></div>
      </div>
    </div>

    <!-- Overlay for better text readability -->
    <div class="absolute inset-0 bg-black bg-opacity-30 z-10"></div>

    <!-- Hero Content -->
    <div class="container mx-auto px-6 py-16 text-center flex flex-col justify-center items-center h-[calc(100vh-128px)] relative z-20">
      <h1 class="text-5xl font-extrabold mb-4 text-white drop-shadow-lg">LIBRARY MANAGEMENT <br>SYSTEM</h1>
      <p class="text-lg text-gray-200 mb-8 drop-shadow-md">Your gateway to a world of knowledge.</p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <a routerLink="/login" class="bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-6 rounded-full transition duration-300 login-tether">LOGIN</a>
        <a href="#features" class="border-2 border-white text-white hover:bg-white hover:text-gray-900 font-semibold py-3 px-6 rounded-full transition duration-300">
          Learn More
        </a>
      </div>
      <div class="mt-8">
        <a href="#features" class="text-gray-300 hover:text-white">
          <svg class="w-8 h-8 animate-bounce drop-shadow-md" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
        </a>
      </div>
    </div>
  </main>

  <!-- Mission Section -->
  <section id="features" class="bg-gradient-to-br from-blue-50 via-orange-50 to-gray-100 py-20 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
      <div class="absolute top-10 left-10 w-32 h-32 bg-orange-300 rounded-full"></div>
      <div class="absolute top-40 right-20 w-24 h-24 bg-blue-300 rounded-full"></div>
      <div class="absolute bottom-20 left-1/4 w-40 h-40 bg-orange-200 rounded-full"></div>
      <div class="absolute bottom-40 right-1/3 w-28 h-28 bg-blue-200 rounded-full"></div>
    </div>
    <div class="container mx-auto px-6 relative z-10">
      <!-- Mission Statement -->
      <div class="max-w-4xl mx-auto text-center mb-16">
        <div class="bg-gradient-to-br from-white via-orange-50 to-blue-50 rounded-2xl shadow-2xl p-12 border-t-8 border-orange-500 relative overflow-hidden">
          <!-- Decorative Elements -->
          <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-200 to-orange-300 rounded-full opacity-20 transform translate-x-16 -translate-y-16"></div>
          <div class="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-blue-200 to-blue-300 rounded-full opacity-20 transform -translate-x-12 translate-y-12"></div>

          <div class="relative z-10">
            <h2 class="text-5xl font-bold bg-gradient-to-r from-gray-800 to-orange-700 bg-clip-text text-transparent mb-4">BENEDICTO COLLEGE</h2>
            <div class="w-24 h-1 bg-gradient-to-r from-orange-400 to-orange-600 mx-auto mb-6"></div>
            <h3 class="text-2xl font-semibold text-orange-600 mb-8 italic">Your Education… Our Mission</h3>
            <p class="text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto">
              As the preferred higher educational institution in the Asia-Pacific, Benedicto College will be a globally competitive institution and a catalyst in nation-building, creating a better quality of life and developing productive members of the society.
            </p>
          </div>
        </div>
      </div>

      <!-- Features Title -->
      <div class="text-center mb-12">
        <div class="bg-gradient-to-r from-orange-500 to-blue-600 rounded-2xl p-8 shadow-xl">
          <h2 class="text-4xl font-bold text-white mb-4">Library Management Excellence</h2>
          <p class="text-orange-100 max-w-2xl mx-auto">Supporting our educational mission with cutting-edge library technology</p>
        </div>
      </div>

      <!-- Academic Programs -->
      <div class="grid md:grid-cols-3 gap-8 mb-16">
        <div class="bg-gradient-to-br from-white to-orange-50 p-8 rounded-xl shadow-lg text-center hover:shadow-2xl hover:scale-105 transition-all duration-300 border-l-4 border-orange-500 relative overflow-hidden">
          <div class="absolute top-0 right-0 w-20 h-20 bg-orange-200 rounded-full opacity-30 transform translate-x-10 -translate-y-10"></div>
          <div class="w-20 h-20 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg relative z-10">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-4 bg-gradient-to-r from-gray-800 to-orange-600 bg-clip-text text-transparent">Academic Excellence</h3>
          <p class="text-gray-600">Comprehensive programs designed to develop globally competitive graduates with strong academic foundation and practical skills.</p>
        </div>

        <div class="bg-gradient-to-br from-white to-blue-50 p-8 rounded-xl shadow-lg text-center hover:shadow-2xl hover:scale-105 transition-all duration-300 border-l-4 border-blue-500 relative overflow-hidden">
          <div class="absolute top-0 right-0 w-20 h-20 bg-blue-200 rounded-full opacity-30 transform translate-x-10 -translate-y-10"></div>
          <div class="w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg relative z-10">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-4 bg-gradient-to-r from-gray-800 to-blue-600 bg-clip-text text-transparent">Global Perspective</h3>
          <p class="text-gray-600">International partnerships and exchange programs preparing students for success in the Asia-Pacific region and beyond.</p>
        </div>

        <div class="bg-gradient-to-br from-white to-green-50 p-8 rounded-xl shadow-lg text-center hover:shadow-2xl hover:scale-105 transition-all duration-300 border-l-4 border-green-500 relative overflow-hidden">
          <div class="absolute top-0 right-0 w-20 h-20 bg-green-200 rounded-full opacity-30 transform translate-x-10 -translate-y-10"></div>
          <div class="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg relative z-10">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-4 bg-gradient-to-r from-gray-800 to-green-600 bg-clip-text text-transparent">Community Impact</h3>
          <p class="text-gray-600">Developing productive members of society through community engagement, leadership development, and social responsibility.</p>
        </div>
      </div>

      <!-- Academic Colleges -->
      <div class="bg-gradient-to-br from-white via-gray-50 to-blue-50 rounded-2xl shadow-2xl p-12 mb-16 relative overflow-hidden border border-gray-200">
        <!-- Background Decorations -->
        <div class="absolute top-0 left-0 w-40 h-40 bg-gradient-to-br from-orange-200 to-orange-300 rounded-full opacity-10 transform -translate-x-20 -translate-y-20"></div>
        <div class="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-200 to-blue-300 rounded-full opacity-10 transform translate-x-16 translate-y-16"></div>

        <div class="relative z-10">
          <div class="text-center mb-12">
            <div class="inline-block bg-gradient-to-r from-orange-500 to-blue-600 rounded-full p-1 mb-4">
              <div class="bg-white rounded-full px-8 py-3">
                <h3 class="text-3xl font-bold bg-gradient-to-r from-orange-600 to-blue-600 bg-clip-text text-transparent">Our Academic Colleges</h3>
              </div>
            </div>
            <p class="text-gray-600 max-w-2xl mx-auto">Discover our comprehensive range of globally-competitive programs across multiple disciplines</p>
          </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <div class="bg-gradient-to-br from-purple-50 via-white to-purple-100 p-6 rounded-xl border-l-4 border-purple-500 hover:shadow-2xl hover:scale-105 transition-all duration-300 relative overflow-hidden">
            <div class="absolute top-0 right-0 w-16 h-16 bg-purple-200 rounded-full opacity-30 transform translate-x-8 -translate-y-8"></div>
            <div class="flex items-center mb-4 relative z-10">
              <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
              <h4 class="text-lg font-bold bg-gradient-to-r from-gray-800 to-purple-600 bg-clip-text text-transparent">College of Arts and Sciences</h4>
            </div>
            <p class="text-gray-600 text-sm">Liberal arts education fostering critical thinking and cultural awareness</p>
          </div>

          <div class="bg-gradient-to-br from-blue-50 via-white to-blue-100 p-6 rounded-xl border-l-4 border-blue-500 hover:shadow-2xl hover:scale-105 transition-all duration-300 relative overflow-hidden">
            <div class="absolute top-0 right-0 w-16 h-16 bg-blue-200 rounded-full opacity-30 transform translate-x-8 -translate-y-8"></div>
            <div class="flex items-center mb-4 relative z-10">
              <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
              </div>
              <h4 class="text-lg font-bold bg-gradient-to-r from-gray-800 to-blue-600 bg-clip-text text-transparent">College of Business and Management</h4>
            </div>
            <p class="text-gray-600 text-sm">Developing future business leaders and entrepreneurs</p>
          </div>

          <div class="bg-gradient-to-br from-green-50 via-white to-green-100 p-6 rounded-xl border-l-4 border-green-500 hover:shadow-2xl hover:scale-105 transition-all duration-300 relative overflow-hidden">
            <div class="absolute top-0 right-0 w-16 h-16 bg-green-200 rounded-full opacity-30 transform translate-x-8 -translate-y-8"></div>
            <div class="flex items-center mb-4 relative z-10">
              <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
              <h4 class="text-lg font-bold bg-gradient-to-r from-gray-800 to-green-600 bg-clip-text text-transparent">College of Computer Studies</h4>
            </div>
            <p class="text-gray-600 text-sm">Cutting-edge technology education for the digital age</p>
          </div>

          <div class="bg-gradient-to-br from-red-50 via-white to-red-100 p-6 rounded-xl border-l-4 border-red-500 hover:shadow-2xl hover:scale-105 transition-all duration-300 relative overflow-hidden">
            <div class="absolute top-0 right-0 w-16 h-16 bg-red-200 rounded-full opacity-30 transform translate-x-8 -translate-y-8"></div>
            <div class="flex items-center mb-4 relative z-10">
              <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
              </div>
              <h4 class="text-lg font-bold bg-gradient-to-r from-gray-800 to-red-600 bg-clip-text text-transparent">College of Education</h4>
            </div>
            <p class="text-gray-600 text-sm">Preparing dedicated educators for quality teaching</p>
          </div>

          <div class="bg-gradient-to-br from-yellow-50 via-white to-yellow-100 p-6 rounded-xl border-l-4 border-yellow-500 hover:shadow-2xl hover:scale-105 transition-all duration-300 relative overflow-hidden">
            <div class="absolute top-0 right-0 w-16 h-16 bg-yellow-200 rounded-full opacity-30 transform translate-x-8 -translate-y-8"></div>
            <div class="flex items-center mb-4 relative z-10">
              <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
              </div>
              <h4 class="text-lg font-bold bg-gradient-to-r from-gray-800 to-yellow-600 bg-clip-text text-transparent">College of Hospitality Management</h4>
            </div>
            <p class="text-gray-600 text-sm">Excellence in hospitality and tourism industry preparation</p>
          </div>

          <div class="bg-gradient-to-br from-indigo-50 via-white to-indigo-100 p-6 rounded-xl border-l-4 border-indigo-500 hover:shadow-2xl hover:scale-105 transition-all duration-300 relative overflow-hidden">
            <div class="absolute top-0 right-0 w-16 h-16 bg-indigo-200 rounded-full opacity-30 transform translate-x-8 -translate-y-8"></div>
            <div class="flex items-center mb-4 relative z-10">
              <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </div>
              <h4 class="text-lg font-bold bg-gradient-to-r from-gray-800 to-indigo-600 bg-clip-text text-transparent">College of Engineering</h4>
            </div>
            <p class="text-gray-600 text-sm">Innovation and technical excellence in engineering disciplines</p>
          </div>
        </div>

        <!-- Additional Programs -->
        <div class="border-t-2 border-gradient-to-r from-orange-300 to-blue-300 pt-8">
          <div class="grid md:grid-cols-2 gap-8">
            <div class="text-center p-8 bg-gradient-to-br from-orange-100 via-white to-orange-50 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border-2 border-orange-200 relative overflow-hidden">
              <div class="absolute top-0 left-0 w-20 h-20 bg-orange-300 rounded-full opacity-20 transform -translate-x-10 -translate-y-10"></div>
              <div class="w-20 h-20 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-xl relative z-10">
                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                </svg>
              </div>
              <h4 class="text-2xl font-bold mb-4 bg-gradient-to-r from-gray-800 to-orange-600 bg-clip-text text-transparent">Senior High School</h4>
              <p class="text-gray-600">Quality secondary education preparing students for higher learning</p>
            </div>

            <div class="text-center p-8 bg-gradient-to-br from-blue-100 via-white to-blue-50 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border-2 border-blue-200 relative overflow-hidden">
              <div class="absolute top-0 left-0 w-20 h-20 bg-blue-300 rounded-full opacity-20 transform -translate-x-10 -translate-y-10"></div>
              <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-xl relative z-10">
                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
              </div>
              <h4 class="text-2xl font-bold mb-4 bg-gradient-to-r from-gray-800 to-blue-600 bg-clip-text text-transparent">Basic Education</h4>
              <p class="text-gray-600">Foundational learning with strong academic and character development</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="py-20 bg-white">
    <div class="container mx-auto px-6 text-center">
      <h2 class="text-4xl font-bold mb-4">About Us</h2>
      <p class="text-lg text-gray-600 max-w-3xl mx-auto">Library Management System is a modern, intuitive, and powerful library management system designed to streamline library operations. Our mission is to provide a seamless experience for both librarians and patrons.</p>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gray-900 py-8 border-t-8 border-orange-500 border-b-8 border-b-orange-500">
    <div class="container mx-auto px-6">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <div class="text-gray-200 mb-4 md:mb-0">
          &copy; 2025 Library Management System. All Rights Reserved.
        </div>
        <div class="flex space-x-6">
          <a href="#" class="text-gray-400 hover:text-orange-400 transition duration-300">Privacy Policy</a>
          <a href="#" class="text-gray-400 hover:text-orange-400 transition duration-300">Terms of Service</a>
          <a href="#" class="text-gray-400 hover:text-orange-400 transition duration-300">Support</a>
        </div>
      </div>
    </div>
  </footer>
</div>
