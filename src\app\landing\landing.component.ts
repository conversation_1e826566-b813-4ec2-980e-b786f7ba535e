import { Component, On<PERSON><PERSON>roy } from '@angular/core';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-landing',
  standalone: true,
  imports: [RouterModule],
  templateUrl: './landing.html',
  styleUrl: './landing.css'
})
export class LandingComponent implements OnDestroy {
  private currentSlide = 0;
  private slides: NodeListOf<Element> | null = null;
  private slideInterval: any;

  ngOnInit() {
    // Mobile menu functionality
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
      mobileMenuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
      });
    }

    // Background slideshow functionality
    this.initializeSlideshow();
  }

  ngOnDestroy() {
    if (this.slideInterval) {
      clearInterval(this.slideInterval);
    }
  }

  private initializeSlideshow() {
    this.slides = document.querySelectorAll('.slide');
    if (this.slides && this.slides.length > 0) {
      // Start the slideshow
      this.slideInterval = setInterval(() => {
        this.nextSlide();
      }, 5000); // Change slide every 5 seconds
    }
  }

  private nextSlide() {
    if (!this.slides) return;

    // Remove active class from current slide
    this.slides[this.currentSlide].classList.remove('active');

    // Move to next slide
    this.currentSlide = (this.currentSlide + 1) % this.slides.length;

    // Add active class to new slide
    this.slides[this.currentSlide].classList.add('active');
  }
}