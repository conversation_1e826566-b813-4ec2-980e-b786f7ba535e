<div class="min-h-screen bg-gradient-to-br from-orange-100 via-orange-50 to-gray-100 relative overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 opacity-15">
    <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
      <defs>
        <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
          <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#f97316" stroke-width="0.5"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#grid)" />
    </svg>
  </div>

  <!-- Floating Decorative Elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div class="absolute top-20 left-10 w-32 h-32 bg-orange-200 rounded-full opacity-20 animate-pulse"></div>
    <div class="absolute top-40 right-20 w-24 h-24 bg-blue-200 rounded-full opacity-30 animate-ping"></div>
    <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-indigo-200 rounded-full opacity-15 animate-pulse"></div>
    <div class="absolute bottom-20 right-1/3 w-28 h-28 bg-orange-300 rounded-full opacity-25 animate-ping"></div>
  </div>

  <!-- Header -->
  <header class="bg-gray-900/90 backdrop-blur-sm shadow-xl border-b-4 border-orange-500 relative z-10">
    <div class="container mx-auto px-6 py-4 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center shadow-lg">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
        </div>
        <div class="text-2xl font-bold text-white">
          <a routerLink="/" class="hover:text-orange-300 transition duration-300">Library Management System</a>
        </div>
      </div>
      <nav>
        <a routerLink="/" class="py-2 px-4 text-white hover:bg-white/10 rounded-lg transition duration-300 backdrop-blur-sm">
          ← Back to Home
        </a>
      </nav>
    </div>
  </header>

  <!-- Login Form Section -->
  <main class="flex-grow flex items-center justify-center py-12 px-6 relative z-10">
    <div class="w-full max-w-md">
      <!-- Login Card -->
      <div class="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-200 p-10 relative overflow-hidden">
        <!-- Decorative Background -->
        <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-100 to-orange-200 rounded-full opacity-50 -translate-y-16 translate-x-16"></div>
        <div class="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full opacity-50 translate-y-12 -translate-x-12"></div>

        <!-- User Avatar -->
        <div class="text-center mb-8 relative z-10">
          <div class="w-20 h-20 bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-xl">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
          <h2 class="text-3xl font-bold text-gray-900 mb-2">Welcome Back</h2>
          <p class="text-gray-600">Access your academic resources</p>
        </div>

        <!-- Login Form -->
        <form class="space-y-6 relative z-10">
          <!-- Student ID Field -->
          <div>
            <label for="studentId" class="block text-sm font-semibold text-gray-700 mb-2">Student ID</label>
            <input
              type="text"
              id="studentId"
              name="studentId"
              placeholder="2000-00000"
              pattern="[0-9]{4}-[0-9]{5}"
              class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-orange-500 focus:ring-2 focus:ring-orange-200 transition duration-300 bg-white/80 backdrop-blur-sm text-gray-900 placeholder-gray-500"
              required
            >
          </div>

          <!-- Password Field -->
          <div>
            <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              placeholder="••••••••••"
              class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-orange-500 focus:ring-2 focus:ring-orange-200 transition duration-300 bg-white/80 backdrop-blur-sm text-gray-900 placeholder-gray-500"
              required
            >
          </div>

          <!-- Remember Me Checkbox -->
          <div class="flex items-center justify-between">
            <label class="flex items-center">
              <input
                type="checkbox"
                class="w-4 h-4 text-orange-500 border-2 border-gray-300 rounded focus:ring-orange-200 focus:ring-2 bg-white"
              >
              <span class="ml-3 text-sm font-medium text-gray-700">Remember me</span>
            </label>
            <a href="#" class="text-sm font-semibold text-orange-600 hover:text-orange-700 transition duration-300">
              Forgot your password?
            </a>
          </div>

          <!-- Login Button -->
          <button
            type="submit"
            class="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-4 px-6 rounded-xl transition duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 focus:outline-none focus:ring-4 focus:ring-orange-200"
          >
            Sign In to Your Account
          </button>
        </form>

        <!-- Help Links -->
        <div class="mt-8 pt-6 border-t border-gray-200 text-center relative z-10">
          <div class="flex items-center justify-center space-x-4 text-sm">
            <a href="#" class="text-gray-600 hover:text-orange-600 font-medium transition duration-300">
              Need Help?
            </a>
            <span class="text-gray-400">|</span>
            <a href="#" class="text-gray-600 hover:text-orange-600 font-medium transition duration-300">
              Request Account Access
            </a>
          </div>
        </div>
      </div>

      <!-- Benedicto College Branding -->
      <div class="mt-8 text-center">
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200 p-6">
          <h3 class="text-lg font-bold text-gray-900 mb-2">Benedicto College</h3>
          <p class="text-sm text-blue-600 font-semibold">Your Education… Our Mission</p>
          <p class="text-xs text-gray-600 mt-2">A globally competitive institution in the Asia-Pacific region</p>
        </div>
      </div>
    </div>
  </main>

  <!-- Footer -->
  <footer class="bg-gray-900/90 backdrop-blur-sm py-6 border-t-4 border-orange-500 relative z-10">
    <div class="container mx-auto px-6 text-center text-white">
      <p>&copy; 2025 Benedicto College Library Management System. All Rights Reserved.</p>
      <p class="text-sm text-gray-300 mt-1">Empowering education through innovative technology</p>
    </div>
  </footer>
</div>
