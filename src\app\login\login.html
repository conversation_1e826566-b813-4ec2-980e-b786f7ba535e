<div class="min-h-screen bg-white text-gray-800 flex flex-col">
  <!-- Header -->
  <header class="bg-blue-200 shadow-md border-b-4 border-orange-500">
    <div class="container mx-auto px-6 py-4 flex justify-between items-center">
      <div class="text-2xl font-bold text-gray-800">
        <a routerLink="/">LibraryPro</a>
      </div>
      <nav>
        <a routerLink="/" class="py-2 px-3 hover:bg-blue-300 rounded">Home</a>
      </nav>
    </div>
  </header>

  <!-- Login Form Section -->
  <main class="flex-grow flex items-center justify-center">
    <div class="bg-gray-100 p-8 rounded-lg shadow-lg w-full max-w-md">
      <h2 class="text-3xl font-bold text-center mb-6">Login to Your Account</h2>
      <form>
        <div class="mb-4">
          <label for="email" class="block text-gray-700 text-sm font-bold mb-2">Email:</label>
          <input type="email" id="email" name="email" placeholder="<EMAIL>" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
        </div>
        <div class="mb-6">
          <label for="password" class="block text-gray-700 text-sm font-bold mb-2">Password:</label>
          <input type="password" id="password" name="password" placeholder="••••••••" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline">
        </div>
        <div class="flex items-center justify-between mb-6">
          <label class="flex items-center text-sm text-gray-700">
            <input type="checkbox" class="form-checkbox text-orange-500">
            <span class="ml-2">Remember me</span>
          </label>
          <a href="#" class="inline-block align-baseline font-bold text-sm text-orange-500 hover:text-orange-600">
            Forgot Password?
          </a>
        </div>
        <div class="mt-6">
          <button type="submit" class="w-full bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-4 rounded-full focus:outline-none focus:shadow-outline transition duration-300">
            Sign In
          </button>
        </div>
      </form>
    </div>
  </main>

  <!-- Footer -->
  <footer class="bg-blue-200 py-6 mt-8 border-t-4 border-orange-500">
    <div class="container mx-auto px-6 text-center text-gray-800">
      &copy; 2025 LibraryPro. All Rights Reserved.
    </div>
  </footer>
</div>
