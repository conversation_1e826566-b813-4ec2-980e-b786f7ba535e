/* Background slideshow styles */
.background-slideshow {
  position: relative;
  width: 100%;
  height: 100%;
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 2s ease-in-out;
}

.slide.active {
  opacity: 1;
}

/* Ensure proper z-index layering */
.relative {
  position: relative;
}

/* Additional styling for better text visibility */
.drop-shadow-lg {
  filter: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));
}

.drop-shadow-md {
  filter: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));
}

/* Tethering/Alive animation for LOGIN button */
.login-tether {
  animation: tether 2s ease-in-out infinite;
  transform-origin: center;
}

@keyframes tether {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.05) rotate(-1deg);
  }
  50% {
    transform: scale(1.02) rotate(1deg);
  }
  75% {
    transform: scale(1.08) rotate(-0.5deg);
  }
}

/* Hover effect enhancement */
.login-tether:hover {
  animation: tether-fast 0.8s ease-in-out infinite;
}

@keyframes tether-fast {
  0%, 100% {
    transform: scale(1.1) rotate(0deg);
  }
  25% {
    transform: scale(1.15) rotate(-2deg);
  }
  50% {
    transform: scale(1.12) rotate(2deg);
  }
  75% {
    transform: scale(1.18) rotate(-1deg);
  }
}